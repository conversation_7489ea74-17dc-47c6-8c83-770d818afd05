#!/usr/bin/env python3
"""
Test skript pro zpracování obrázků pomocí preprocessing pipeline.
Načítá soubory ze složky Pics_test a zpracovává je pomocí run_pipeline z preprocess_pipeline.py.
Zpracované soubory ukládá do složky Tested.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import sys
from typing import List, Tuple
import fitz  # PyMuPDF pro PDF soubory

from scripts.preprocess_pipeline import PreprocessPipeline

# Přidání scripts složky do Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))

class TestPipeline:
    def __init__(self, input_dir: str = "Pics_test", output_dir: str = "Tested"):
        """
        Inicializace test pipeline.
        
        Args:
            input_dir: <PERSON><PERSON>žka s testovacími obrázky
            output_dir: S<PERSON>žka pro uložení zpracovaných obrázků
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        
        # Vytvoření výstupní složky pokud neexistuje
        self.output_dir.mkdir(exist_ok=True)
        
        # Podporované formáty obrázků
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        self.pdf_extension = '.pdf'
        
    def get_test_files(self) -> List[Path]:
        """
        Získá seznam všech testovacích souborů ze vstupní složky.
        
        Returns:
            Seznam cest k souborům
        """
        files = []
        if not self.input_dir.exists():
            print(f"Chyba: Složka {self.input_dir} neexistuje!")
            return files
            
        for file_path in self.input_dir.iterdir():
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in self.image_extensions or ext == self.pdf_extension:
                    files.append(file_path)
                    
        return sorted(files)
    
    def load_image(self, file_path: Path) -> Tuple[np.ndarray, str]:
        """
        Načte obrázek ze souboru.
        
        Args:
            file_path: Cesta k souboru
            
        Returns:
            Tuple (obrázek jako numpy array, error message pokud nastala chyba)
        """
        try:
            ext = file_path.suffix.lower()
            
            if ext == self.pdf_extension:
                # Zpracování PDF souboru
                return self.load_pdf_as_image(file_path)
            else:
                # Zpracování obrázku
                image = cv2.imread(str(file_path))
                if image is None:
                    return None, f"Nepodařilo se načíst obrázek: {file_path}"
                return image, ""
                
        except Exception as e:
            return None, f"Chyba při načítání {file_path}: {str(e)}"
    
    def load_pdf_as_image(self, pdf_path: Path) -> Tuple[np.ndarray, str]:
        """
        Načte první stránku PDF jako obrázek.
        
        Args:
            pdf_path: Cesta k PDF souboru
            
        Returns:
            Tuple (obrázek jako numpy array, error message pokud nastala chyba)
        """
        try:
            doc = fitz.open(str(pdf_path))
            if len(doc) == 0:
                return None, f"PDF soubor {pdf_path} je prázdný"
                
            # Načtení první stránky
            page = doc[0]
            
            # Konverze na obrázek s vysokým rozlišením
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom pro lepší kvalitu
            pix = page.get_pixmap(matrix=mat)
            
            # Konverze na numpy array
            img_data = pix.tobytes("ppm")
            nparr = np.frombuffer(img_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            doc.close()
            
            if image is None:
                return None, f"Nepodařilo se konvertovat PDF {pdf_path} na obrázek"
                
            return image, ""
            
        except Exception as e:
            return None, f"Chyba při zpracování PDF {pdf_path}: {str(e)}"
    
    def save_processed_image(self, image: np.ndarray, original_path: Path) -> bool:
        """
        Uloží zpracovaný obrázek do výstupní složky.
        
        Args:
            image: Zpracovaný obrázek
            original_path: Původní cesta k souboru
            
        Returns:
            True pokud se uložení podařilo, False jinak
        """
        try:
            # Vytvoření názvu výstupního souboru
            base_name = original_path.stem
            output_path = self.output_dir / f"{base_name}_processed.png"
            
            # Uložení obrázku
            success = cv2.imwrite(str(output_path), image)
            
            if success:
                print(f"✓ Uloženo: {output_path}")
                return True
            else:
                print(f"✗ Chyba při ukládání: {output_path}")
                return False
                
        except Exception as e:
            print(f"✗ Chyba při ukládání {original_path.name}: {str(e)}")
            return False
    
    def process_single_file(self, file_path: Path) -> bool:
        """
        Zpracuje jeden soubor pomocí preprocessing pipeline.
        
        Args:
            file_path: Cesta k souboru
            
        Returns:
            True pokud se zpracování podařilo, False jinak
        """
        print(f"Zpracovávám: {file_path.name}")
        
        # Načtení obrázku
        image, error = self.load_image(file_path)
        if image is None:
            print(f"✗ {error}")
            return False
        
        try:
            # Zpracování pomocí preprocessing pipeline
            processed_image = PreprocessPipeline.run_pipeline(
                image=image,
                current_dpi=300,
                unsharp=True,
                morph_op=None  # Můžete změnit na "close", "erode", "dilate" nebo None
            )
            
            # Uložení zpracovaného obrázku
            return self.save_processed_image(processed_image, file_path)
            
        except Exception as e:
            print(f"✗ Chyba při zpracování {file_path.name}: {str(e)}")
            return False
    
    def run_test(self) -> None:
        """
        Spustí test na všech souborech ve vstupní složce.
        """
        print(f"=== Test Pipeline ===")
        print(f"Vstupní složka: {self.input_dir}")
        print(f"Výstupní složka: {self.output_dir}")
        print()
        
        # Získání seznamu souborů
        test_files = self.get_test_files()
        
        if not test_files:
            print("Žádné testovací soubory nenalezeny!")
            return
        
        print(f"Nalezeno {len(test_files)} souborů k zpracování:")
        for file_path in test_files:
            print(f"  - {file_path.name}")
        print()
        
        # Zpracování souborů
        successful = 0
        failed = 0
        
        for file_path in test_files:
            if self.process_single_file(file_path):
                successful += 1
            else:
                failed += 1
        
        # Výsledky
        print()
        print(f"=== Výsledky ===")
        print(f"Úspěšně zpracováno: {successful}")
        print(f"Chyby: {failed}")
        print(f"Celkem: {len(test_files)}")


def main():
    """
    Hlavní funkce pro spuštění testu.
    """
    # Vytvoření instance test pipeline
    test_pipeline = TestPipeline()
    
    # Spuštění testu
    test_pipeline.run_test()


if __name__ == "__main__":
    main()
