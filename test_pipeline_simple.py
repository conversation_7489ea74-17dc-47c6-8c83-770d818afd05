#!/usr/bin/env python3
"""
Jednoduchý test skript pro zpracování obrázků pomocí preprocessing pipeline.
Načítá pouze obrázky ze složky Pics_test a zpracovává je pomocí run_pipeline z preprocess_pipeline.py.
Zpracované soubory ukládá do složky Tested.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import sys
from typing import List

# Přidání scripts složky do Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))

from preprocess_pipeline import PreprocessPipeline


class SimpleTestPipeline:
    def __init__(self, input_dir: str = "Pics_test", output_dir: str = "Tested"):
        """
        Inicializace test pipeline.
        
        Args:
            input_dir: Složka s testovacími obrázky
            output_dir: Složka pro uložení zpracovaných obrázků
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        
        # Vytvoření výstupní složky pokud neexistuje
        self.output_dir.mkdir(exist_ok=True)
        
        # Podporované formáty obrázků (pouze obrázky, ne PDF)
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
    def get_image_files(self) -> List[Path]:
        """
        Získá seznam všech obrázků ze vstupní složky.
        
        Returns:
            Seznam cest k obrázkům
        """
        files = []
        if not self.input_dir.exists():
            print(f"Chyba: Složka {self.input_dir} neexistuje!")
            return files
            
        for file_path in self.input_dir.iterdir():
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in self.image_extensions:
                    files.append(file_path)
                    
        return sorted(files)
    
    def load_image(self, file_path: Path) -> np.ndarray:
        """
        Načte obrázek ze souboru.
        
        Args:
            file_path: Cesta k souboru
            
        Returns:
            Obrázek jako numpy array nebo None při chybě
        """
        try:
            image = cv2.imread(str(file_path))
            if image is None:
                print(f"✗ Nepodařilo se načíst obrázek: {file_path}")
                return None
            return image
        except Exception as e:
            print(f"✗ Chyba při načítání {file_path}: {str(e)}")
            return None
    
    def save_processed_image(self, image: np.ndarray, original_path: Path) -> bool:
        """
        Uloží zpracovaný obrázek do výstupní složky.
        
        Args:
            image: Zpracovaný obrázek
            original_path: Původní cesta k souboru
            
        Returns:
            True pokud se uložení podařilo, False jinak
        """
        try:
            # Vytvoření názvu výstupního souboru
            base_name = original_path.stem
            output_path = self.output_dir / f"{base_name}_processed.png"
            
            # Uložení obrázku
            success = cv2.imwrite(str(output_path), image)
            
            if success:
                print(f"✓ Uloženo: {output_path}")
                return True
            else:
                print(f"✗ Chyba při ukládání: {output_path}")
                return False
                
        except Exception as e:
            print(f"✗ Chyba při ukládání {original_path.name}: {str(e)}")
            return False
    
    def process_single_file(self, file_path: Path) -> bool:
        """
        Zpracuje jeden obrázek pomocí preprocessing pipeline.
        
        Args:
            file_path: Cesta k souboru
            
        Returns:
            True pokud se zpracování podařilo, False jinak
        """
        print(f"Zpracovávám: {file_path.name}")
        
        # Načtení obrázku
        image = self.load_image(file_path)
        if image is None:
            return False
        
        try:
            # Zpracování pomocí preprocessing pipeline
            processed_image = PreprocessPipeline.run_pipeline(
                image=image,
                current_dpi=300,
                unsharp=True,
                morph_op="open"  # Můžete změnit na "close", "erode", "dilate" nebo None
            )
            
            # Uložení zpracovaného obrázku
            return self.save_processed_image(processed_image, file_path)
            
        except Exception as e:
            print(f"✗ Chyba při zpracování {file_path.name}: {str(e)}")
            return False
    
    def run_test(self) -> None:
        """
        Spustí test na všech obrázcích ve vstupní složce.
        """
        print(f"=== Simple Test Pipeline ===")
        print(f"Vstupní složka: {self.input_dir}")
        print(f"Výstupní složka: {self.output_dir}")
        print("Poznámka: Zpracovávají se pouze obrázky (JPEG, PNG, BMP, TIFF)")
        print()
        
        # Získání seznamu obrázků
        image_files = self.get_image_files()
        
        if not image_files:
            print("Žádné obrázky nenalezeny!")
            return
        
        print(f"Nalezeno {len(image_files)} obrázků k zpracování:")
        for file_path in image_files:
            print(f"  - {file_path.name}")
        print()
        
        # Zpracování obrázků
        successful = 0
        failed = 0
        
        for file_path in image_files:
            if self.process_single_file(file_path):
                successful += 1
            else:
                failed += 1
        
        # Výsledky
        print()
        print(f"=== Výsledky ===")
        print(f"Úspěšně zpracováno: {successful}")
        print(f"Chyby: {failed}")
        print(f"Celkem: {len(image_files)}")


def main():
    """
    Hlavní funkce pro spuštění testu.
    """
    # Vytvoření instance test pipeline
    test_pipeline = SimpleTestPipeline()
    
    # Spuštění testu
    test_pipeline.run_test()


if __name__ == "__main__":
    main()
