import cv2
import numpy as np
from typing import Tuple

class PreprocessPipeline:
    TARGET_DPI = 300

    @staticmethod
    def _ensure_odd(n: int) -> int:
        return n if n % 2 == 1 else n + 1

    # --- FÁZE 1: Geometrická a globální příprava ---
    @staticmethod
    def to_grayscale(image: np.ndarray) -> np.ndarray:
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return image

    @staticmethod
    def normalize_dpi(image: np.ndarray, current_dpi: int = 300) -> np.ndarray:
        if current_dpi == PreprocessPipeline.TARGET_DPI:
            return image
        scale = PreprocessPipeline.TARGET_DPI / current_dpi
        new_size = (int(image.shape[1] * scale), int(image.shape[0] * scale))
        return cv2.resize(image, new_size, interpolation=cv2.INTER_CUBIC)

    @staticmethod
    def detect_deskew_angle(image: np.ndarray) -> float:
        norm = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX)
        _, binary = cv2.threshold(norm, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        if np.mean(binary) > 127:
            binary = cv2.bitwise_not(binary)
        edges = cv2.Canny(binary, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi / 180, 200)
        if lines is None:
            return 0.0
        angles = [(theta * 180 / np.pi) - 90 for rho, theta in lines[:, 0] if -45 < (theta * 180 / np.pi) - 90 < 45]
        return float(np.median(angles)) if angles else 0.0

    @staticmethod
    def apply_deskew(image: np.ndarray, angle: float) -> np.ndarray:
        (h, w) = image.shape[:2]
        center = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D(center, angle, 1.0)
        return cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)

    # --- FÁZE 2: Vylepšení kvality ---
    @staticmethod
    def denoise(image: np.ndarray, h: int = 10) -> np.ndarray:
        return cv2.fastNlMeansDenoising(image, None, h, 7, 21)

    @staticmethod
    def auto_tone_mapping(image: np.ndarray, low_percent: float = 1.0, high_percent: float = 99.0, gamma: float = None) -> np.ndarray:
        low_val = np.percentile(image, low_percent)
        high_val = np.percentile(image, high_percent)
        stretched = np.clip((image - low_val) * 255.0 / (high_val - low_val), 0, 255).astype(np.uint8)
        if gamma is not None and gamma != 1.0:
            inv_gamma = 1.0 / gamma
            table = np.array([(i / 255.0) ** inv_gamma * 255 for i in range(256)]).astype("uint8")
            stretched = cv2.LUT(stretched, table)
        return stretched

    @staticmethod
    def clahe(image: np.ndarray, clip_limit: float = 2.0, tile_grid_size: Tuple[int, int] = (8, 8)) -> np.ndarray:
        clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
        return clahe.apply(image)

    @staticmethod
    def unsharp_mask(image: np.ndarray, ksize: Tuple[int, int] = (5, 5), alpha: float = 1.5, beta: float = -0.5, gamma: float = 0) -> np.ndarray:
        blurred = cv2.GaussianBlur(image, ksize, 0)
        return cv2.addWeighted(image, alpha, blurred, beta, gamma)

    # --- FÁZE 3: Binarizace a čištění ---
    @staticmethod
    def sauvola_binarization(image: np.ndarray, window: int = 25, k: float = 0.2, R: float = 128.0) -> np.ndarray:
        img = image.astype(np.float32)
        w = PreprocessPipeline._ensure_odd(window)
        mean = cv2.boxFilter(img, ddepth=-1, ksize=(w, w), normalize=True)
        sqmean = cv2.boxFilter(img * img, ddepth=-1, ksize=(w, w), normalize=True)
        var = np.maximum(sqmean - mean * mean, 0.0)
        std = np.sqrt(var)
        # Sauvola threshold
        thresh = mean * (1 + k * ((std / max(R, 1e-6)) - 1))
        # Binarize
        binary = (img > thresh).astype(np.uint8) * 255
        return binary

    @staticmethod
    def median_filter(image: np.ndarray, ksize: int = 3) -> np.ndarray:
        return cv2.medianBlur(image, ksize)

    @staticmethod
    def morphology(image: np.ndarray, operation: str = "open", ksize: int = 3, iterations: int = 1) -> np.ndarray:
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (ksize, ksize))
        if operation == "open":
            return cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel, iterations=iterations)
        elif operation == "close":
            return cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel, iterations=iterations)
        elif operation == "erode":
            return cv2.erode(image, kernel, iterations=iterations)
        elif operation == "dilate":
            return cv2.dilate(image, kernel, iterations=iterations)
        else:
            raise ValueError(f"Unknown morphology operation: {operation}")

    # --- ORCHESTRACE VŠECH FÁZÍ ---
    @staticmethod
    def run_pipeline(image: np.ndarray, current_dpi: int = 300, unsharp: bool = True, morph_op: str = None) -> np.ndarray:
        """
        Kompletní preprocessing pipeline (Fáze 1–3) pro OCR.
        - unsharp: zda aplikovat unsharp mask
        - morph_op: název morfologické operace nebo None
        """
        # Fáze 1
        gray = PreprocessPipeline.to_grayscale(image)
        gray = PreprocessPipeline.normalize_dpi(gray, current_dpi=current_dpi)
        #angle = PreprocessPipeline.detect_deskew_angle(gray)
        #gray = PreprocessPipeline.apply_deskew(gray, angle)

        # Fáze 2
        gray = PreprocessPipeline.denoise(gray)
        gray = PreprocessPipeline.auto_tone_mapping(gray)
        gray = PreprocessPipeline.clahe(gray)
        if unsharp:
            gray = PreprocessPipeline.unsharp_mask(gray)

        # Fáze 3
        binary = PreprocessPipeline.sauvola_binarization(gray)
        binary = PreprocessPipeline.median_filter(binary)
        if morph_op is not None:
            binary = PreprocessPipeline.morphology(binary, operation=morph_op)

        return binary
